#!/usr/bin/env python3
"""
Test script to verify fullscreen infinite loop fix
This script simulates the problematic fullscreen behavior to test our fixes
"""

import pygame
import sys
import os
from time import time

# Force SDL to use X11 if not already set (prevents Wayland crashes)
if os.environ.get("SDL_VIDEODRIVER") != "x11":
    os.environ["SDL_VIDEODRIVER"] = "x11"

def log_debug(message):
    print(f"DEBUG: {message}")

def test_fullscreen_fix():
    """Test the fullscreen fix logic"""
    
    # Initialize pygame
    pygame.init()
    
    # Set up initial window
    screen = pygame.display.set_mode((800, 600), pygame.OPENGL | pygame.DOUBLEBUF | pygame.RESIZABLE)
    pygame.display.set_caption("Fullscreen Fix Test - Press F11 to test, ESC to quit")
    
    # Fullscreen state variables (matching main.py)
    fullscreen = False
    prev_window_size = (800, 600)
    last_fullscreen_toggle = 0
    fullscreen_transition_in_progress = False
    
    clock = pygame.time.Clock()
    running = True
    
    log_debug("Test started. Press F11 rapidly to test fullscreen fix, ESC to quit")
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_F11:
                    # EXACT SAME LOGIC AS MAIN.PY (with our fixes)
                    current_time = time()
                    if current_time - last_fullscreen_toggle < 1.0:  # 1 second debounce
                        log_debug(f"Ignoring F11 - too soon after last toggle (debounce: {current_time - last_fullscreen_toggle:.2f}s)")
                        continue
                    if fullscreen_transition_in_progress:
                        log_debug("Ignoring F11 - fullscreen transition already in progress")
                        continue
                    
                    # Set debounce timer and transition flag IMMEDIATELY
                    last_fullscreen_toggle = current_time
                    fullscreen_transition_in_progress = True
                    log_debug(f"F11 debounce timer set, transition flag enabled")
                    
                    new_fullscreen_state = not fullscreen
                    log_debug(f"F11 pressed - toggling fullscreen from {fullscreen} to: {new_fullscreen_state}")
                    
                    # Check if we're already in the desired state
                    current_surface = pygame.display.get_surface()
                    current_flags = current_surface.get_flags() if current_surface else 0
                    is_currently_fullscreen = bool(current_flags & pygame.FULLSCREEN)
                    
                    if is_currently_fullscreen == new_fullscreen_state:
                        log_debug(f"Already in desired fullscreen state ({new_fullscreen_state}), ignoring toggle")
                        fullscreen_transition_in_progress = False
                        continue
                    
                    try:
                        fullscreen = new_fullscreen_state
                        
                        if fullscreen:
                            # Save current window size
                            screen = pygame.display.get_surface()
                            prev_window_size = screen.get_size() if screen else (800, 600)
                            
                            # Try to enter fullscreen
                            log_debug(f"Attempting fullscreen mode...")
                            screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN | pygame.OPENGL | pygame.DOUBLEBUF)
                            log_debug(f"Fullscreen mode set successfully")
                            pygame.mouse.set_visible(False)
                        else:
                            # Return to windowed mode
                            log_debug(f"Returning to windowed mode: {prev_window_size}")
                            screen = pygame.display.set_mode(prev_window_size, pygame.OPENGL | pygame.DOUBLEBUF | pygame.RESIZABLE)
                            pygame.mouse.set_visible(True)
                            log_debug(f"Windowed mode restored successfully")
                            
                    except pygame.error as e:
                        log_debug(f"*** ERROR DURING FULLSCREEN TRANSITION: {e} ***")
                        log_debug("*** Reverting to windowed mode ***")
                        
                        # Force fullscreen state to False
                        fullscreen = False
                        
                        # Attempt emergency recovery
                        try:
                            emergency_size = prev_window_size
                            screen = pygame.display.set_mode(emergency_size, pygame.OPENGL | pygame.DOUBLEBUF | pygame.RESIZABLE)
                            pygame.mouse.set_visible(True)
                            log_debug(f"Emergency recovery successful: {emergency_size}")
                        except pygame.error as e2:
                            log_debug(f"*** EMERGENCY RECOVERY FAILED: {e2} ***")
                        
                        # Extend debounce time after error
                        last_fullscreen_toggle = current_time + 3.0  # 3 second additional delay
                        
                    finally:
                        # Always reset the transition flag
                        fullscreen_transition_in_progress = False
                        log_debug("Fullscreen transition completed, flag reset")
                    
                    # Skip to next event
                    continue
        
        # Clear screen and display
        screen.fill((0, 0, 0))
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    log_debug("Test completed successfully")

if __name__ == "__main__":
    test_fullscreen_fix()
