#!/usr/bin/env python3
"""
Quick test to verify fullscreen toggle fix
"""
import pygame
import sys
import os
from time import time

# Force SDL to use X11 if not already set (prevents Wayland crashes)
if os.environ.get("SDL_VIDEODRIVER") != "x11":
    os.environ["SDL_VIDEODRIVER"] = "x11"

def main():
    pygame.init()
    
    # Create a simple window
    screen = pygame.display.set_mode((800, 600), pygame.RESIZABLE)
    pygame.display.set_caption("Fullscreen Toggle Test")
    
    # Fullscreen state tracking
    fullscreen = False
    last_fullscreen_toggle = 0
    fullscreen_transition_in_progress = False
    
    clock = pygame.time.Clock()
    
    print("Press F11 to toggle fullscreen, Q to quit")
    
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_q:
                    running = False
                elif event.key == pygame.K_F11:
                    # Add debounce to prevent rapid fullscreen toggles
                    current_time = time()
                    if current_time - last_fullscreen_toggle < 1.0:  # 1 second debounce
                        print("Ignoring F11 - too soon after last toggle")
                        continue
                    if fullscreen_transition_in_progress:
                        print("Ignoring F11 - fullscreen transition already in progress")
                        continue
                    last_fullscreen_toggle = current_time
                    fullscreen_transition_in_progress = True
                    
                    new_fullscreen_state = not fullscreen
                    print(f"F11 pressed - toggling fullscreen from {fullscreen} to: {new_fullscreen_state}")
                    
                    # Check if we're already in the desired state
                    current_surface = pygame.display.get_surface()
                    current_flags = current_surface.get_flags() if current_surface else 0
                    is_currently_fullscreen = bool(current_flags & pygame.FULLSCREEN)
                    
                    if is_currently_fullscreen == new_fullscreen_state:
                        print(f"Already in desired fullscreen state ({new_fullscreen_state}), ignoring toggle")
                        fullscreen_transition_in_progress = False
                        continue
                    
                    fullscreen = new_fullscreen_state
                    
                    try:
                        if fullscreen:
                            print("Entering fullscreen...")
                            screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
                        else:
                            print("Exiting fullscreen...")
                            screen = pygame.display.set_mode((800, 600), pygame.RESIZABLE)
                        print(f"Fullscreen toggle successful: {fullscreen}")
                    except pygame.error as e:
                        print(f"Error toggling fullscreen: {e}")
                        fullscreen = not fullscreen  # Revert state
                    finally:
                        fullscreen_transition_in_progress = False
        
        # Simple rendering
        screen.fill((50, 50, 100))
        
        # Show current state
        font = pygame.font.Font(None, 36)
        state_text = f"Fullscreen: {fullscreen}"
        text_surface = font.render(state_text, True, (255, 255, 255))
        screen.blit(text_surface, (10, 10))
        
        instructions = font.render("Press F11 to toggle, Q to quit", True, (255, 255, 255))
        screen.blit(instructions, (10, 50))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()

if __name__ == "__main__":
    main()
