KVWF      solar_flare   cosmic    Solar flare energy burst pattern   high   KarmaViz Advanced Generator   1.0H  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }

    // Original waveform logic converted to inline
    if (!waveform_enabled || waveform_length <= 0) {
            float waveform_value = 0.0;
        }
    
        float norm_x = clamp(x_coord, 0.0, 1.0);
        float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
        // Solar flare eruption
        float flare_center = 0.3 + sin(time * 0.5) * 0.2;
        float flare_intensity = abs(sample);
        float distance_to_flare = abs(norm_x - flare_center);
    
        // Magnetic field lines
        float magnetic_field = sin(norm_x * 15.708 + time * 3.0) * 0.2;
    
        // Plasma ejection
        float plasma = exp(-distance_to_flare * 5.0) * flare_intensity;
        plasma += exp(-distance_to_flare * 2.0) * flare_intensity * 0.3;
    
        float waveform_value = (plasma + magnetic_field * flare_intensity) * waveform_scale;

    // Render as horizontal waveform line
    float waveform_y = 0.5 + waveform_value * 0.25; // Center at 0.5 with amplitude scaling

    // Create a line with thickness and glow
    float line_thickness = 0.02;
    float glow_radius = 0.08;
    float distance_to_line = abs(y_coord - waveform_y);

    // Core line intensity
    float core_intensity = smoothstep(line_thickness, 0.0, distance_to_line);

    // Glow effect
    float glow_intensity = exp(-distance_to_line * distance_to_line / (glow_radius * glow_radius)) * 0.3;

    return clamp(core_intensity + glow_intensity, 0.0, 1.0);
}