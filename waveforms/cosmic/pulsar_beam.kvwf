KVWF      pulsar_beam   cosmic   Rotating pulsar beam sweep   high   KarmaViz Advanced Generator   1.0  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }

    // Original waveform logic converted to inline
    if (!waveform_enabled || waveform_length <= 0) {
            float waveform_value = 0.0;
        }
    
        float norm_x = clamp(x_coord, 0.0, 1.0);
        float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
        // Pulsar rotation
        float rotation_phase = time * 2.0;
        float beam_angle = fract(rotation_phase);
    
        // Beam sweep
        float beam_width = 0.05 + abs(sample) * 0.03;
        float beam_center = beam_angle;
        float beam_intensity = exp(-pow(abs(norm_x - beam_center) / beam_width, 2.0));
    
        // Add secondary beam (180 degrees out of phase)
        float secondary_center = fract(beam_angle + 0.5);
        beam_intensity += exp(-pow(abs(norm_x - secondary_center) / beam_width, 2.0)) * 0.6;
    
        // Neutron star magnetic field modulation
        float magnetic_mod = sin(norm_x * 50.0 + time * 10.0) * 0.1 + 0.9;
    
        float waveform_value = beam_intensity * magnetic_mod * abs(sample) * waveform_scale;

    // Render as horizontal waveform line
    float waveform_y = 0.5 + waveform_value * 0.25; // Center at 0.5 with amplitude scaling

    // Create a line with thickness and glow
    float line_thickness = 0.02;
    float glow_radius = 0.08;
    float distance_to_line = abs(y_coord - waveform_y);

    // Core line intensity
    float core_intensity = smoothstep(line_thickness, 0.0, distance_to_line);

    // Glow effect
    float glow_intensity = exp(-distance_to_line * distance_to_line / (glow_radius * glow_radius)) * 0.3;

    return clamp(core_intensity + glow_intensity, 0.0, 1.0);
}