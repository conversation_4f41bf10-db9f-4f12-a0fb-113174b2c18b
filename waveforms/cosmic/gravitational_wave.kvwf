KVWF      gravitational_wave   cosmic'   Gravitational wave ripples in spacetime   high   KarmaViz Advanced Generator   1.0"  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }

    // Original waveform logic converted to inline
    if (!waveform_enabled || waveform_length <= 0) {
            float waveform_value = 0.0;
        }
    
        float norm_x = clamp(x_coord, 0.0, 1.0);
        float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
        // Gravitational wave chirp
        float frequency = 35.0 + abs(sample) * 200.0; // Hz range of LIGO detection
        float amplitude = abs(sample) * 1e-21; // Strain amplitude
    
        // Chirp pattern (frequency increases as masses spiral in)
        float chirp_rate = abs(sample) * 10.0;
        float instantaneous_freq = frequency * (1.0 + chirp_rate * time);
    
        // Plus and cross polarizations
        float h_plus = amplitude * sin(norm_x * instantaneous_freq * 6.28318 + time * instantaneous_freq);
        float h_cross = amplitude * cos(norm_x * instantaneous_freq * 6.28318 + time * instantaneous_freq) * 0.7;
    
        // Combine polarizations
        float gw_strain = h_plus + h_cross;
    
        float waveform_value = gw_strain * 1e18 * waveform_scale;

    // Render as horizontal waveform line
    float waveform_y = 0.5 + waveform_value * 0.25; // Center at 0.5 with amplitude scaling

    // Create a line with thickness and glow
    float line_thickness = 0.02;
    float glow_radius = 0.08;
    float distance_to_line = abs(y_coord - waveform_y);

    // Core line intensity
    float core_intensity = smoothstep(line_thickness, 0.0, distance_to_line);

    // Glow effect
    float glow_intensity = exp(-distance_to_line * distance_to_line / (glow_radius * glow_radius)) * 0.3;

    return clamp(core_intensity + glow_intensity, 0.0, 1.0);
}