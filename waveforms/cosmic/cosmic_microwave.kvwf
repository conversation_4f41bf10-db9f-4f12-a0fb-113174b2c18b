KVWF      cosmic_microwave   cosmic%   Cosmic microwave background radiation   medium   KarmaViz Advanced Generator   1.0A  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }

    // Original waveform logic converted to inline
    if (!waveform_enabled || waveform_length <= 0) {
            float waveform_value = 0.0;
        }
    
        float norm_x = clamp(x_coord, 0.0, 1.0);
        float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
        // CMB temperature fluctuations
        float cmb_base = 2.725; // Kelvin
        float fluctuation = 0.0;
    
        // Multiple scales of fluctuation
        for (int i = 1; i <= 8; i++) {
            float scale = pow(2.0, float(i));
            float amplitude = 1.0 / scale;
            fluctuation += sin(norm_x * scale * 6.28318 + time * 0.1) * amplitude;
        }
    
        // Modulate by audio
        float cmb_signal = fluctuation * abs(sample) * 0.001; // Microkelvin scale
    
        float waveform_value = cmb_signal * 1000.0 * waveform_scale;

    // Render as horizontal waveform line
    float waveform_y = 0.5 + waveform_value * 0.25; // Center at 0.5 with amplitude scaling

    // Create a line with thickness and glow
    float line_thickness = 0.02;
    float glow_radius = 0.08;
    float distance_to_line = abs(y_coord - waveform_y);

    // Core line intensity
    float core_intensity = smoothstep(line_thickness, 0.0, distance_to_line);

    // Glow effect
    float glow_intensity = exp(-distance_to_line * distance_to_line / (glow_radius * glow_radius)) * 0.3;

    return clamp(core_intensity + glow_intensity, 0.0, 1.0);
}