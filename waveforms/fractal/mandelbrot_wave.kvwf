KVWF      mandelbrot_wave   fractal0   Waveform modulated by Mandelbrot-like iterations   high   KarmaViz Generator   1.0h  float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }

    // Original waveform logic converted to inline
    if (!waveform_enabled || waveform_length <= 0) {
            float waveform_value = 0.0;
        }
    
        float norm_x = clamp(x_coord, 0.0, 1.0);
        float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
        // Simple fractal-like iteration
        float z = norm_x * 2.0 - 1.0;
        float c = sample * 0.5;
        for (int i = 0; i < 5; i++) {
            z = z * z + c;
            if (abs(z) > 2.0) break;
        }
    
        float fractal_mod = clamp(abs(z) / 2.0, 0.0, 1.0);
        float waveform_value = sample * fractal_mod * waveform_scale;

    // Render as horizontal waveform line
    float waveform_y = 0.5 + waveform_value * 0.25; // Center at 0.5 with amplitude scaling

    // Create a line with thickness and glow
    float line_thickness = 0.02;
    float glow_radius = 0.08;
    float distance_to_line = abs(y_coord - waveform_y);

    // Core line intensity
    float core_intensity = smoothstep(line_thickness, 0.0, distance_to_line);

    // Glow effect
    float glow_intensity = exp(-distance_to_line * distance_to_line / (glow_radius * glow_radius)) * 0.3;

    return clamp(core_intensity + glow_intensity, 0.0, 1.0);
}