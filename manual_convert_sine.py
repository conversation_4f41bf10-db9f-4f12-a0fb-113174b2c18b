#!/usr/bin/env python3
"""
Manual conversion of sine_wave to test the XY format
"""

from modules.waveform_manager import Waveform<PERSON>ana<PERSON>, WaveformInfo

def main():
    # Create the new sine wave with XY function
    sine_wave_xy_code = """float compute_waveform_intensity_at_xy(float x_coord, float y_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    // Get the waveform value at this x coordinate using original logic
    float waveform_value = compute_waveform_at_x(x_coord);
    
    // Render as horizontal waveform line
    float waveform_y = 0.5 + waveform_value * 0.25; // Center at 0.5 with amplitude scaling
    
    // Create a line with thickness and glow
    float line_thickness = 0.02;
    float glow_radius = 0.08;
    float distance_to_line = abs(y_coord - waveform_y);
    
    // Core line intensity
    float core_intensity = smoothstep(line_thickness, 0.0, distance_to_line);
    
    // Glow effect
    float glow_intensity = exp(-distance_to_line * distance_to_line / (glow_radius * glow_radius)) * 0.3;
    
    return clamp(core_intensity + glow_intensity, 0.0, 1.0);
}

float compute_waveform_at_x(float x_coord) {
    if (!waveform_enabled || waveform_length <= 0) {
        return 0.0;
    }
    
    float norm_x = clamp(x_coord, 0.0, 1.0);
    float sample = texture(waveform_data, vec2(norm_x, 0.5)).r;
    
    // Apply sine wave transformation
    return sin(sample * 3.14159 * 2.0) * waveform_scale;
}"""

    # Create waveform info
    sine_wave_info = WaveformInfo(
        name="sine_wave",
        category="basic",
        description="Classic sine wave transformation of audio data (XY converted)",
        complexity="low",
        author="KarmaViz Generator",
        version="1.0",
        glsl_code=sine_wave_xy_code,
        is_builtin=True
    )
    
    # Save it
    wm = WaveformManager()
    success = wm.save_waveform(sine_wave_info, overwrite=True, subdirectory="basic")
    
    if success:
        print("✓ Successfully converted sine_wave manually")
    else:
        print("✗ Failed to save sine_wave")

if __name__ == "__main__":
    main()
